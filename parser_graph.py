import json
import httpx
import requests
import csv
import os
from datetime import datetime
from typing import TypedDict, Dict, Any, List, Optional, Literal
from langgraph.graph import StateGraph, START, END
import operator
from typing import Annotated

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_anthropic import <PERSON>t<PERSON>nt<PERSON>
from langchain_deepseek import ChatDeepSeek
from langchain_openai import ChatOpenAI

from dotenv import load_dotenv
load_dotenv()

from dataclasses import dataclass, field

# Define InputData dataclass
@dataclass
class InputData:
    variable_name: str
    data_path: str
    data_description: str
    sandbox_file_name: str 
    def __hash__(self):
        # Make hashable if needed (e.g., for sets), ensure fields are hashable
        return hash((self.variable_name, self.data_path, self.data_description, self.sandbox_file_name))


# Default prompt to use if partner is not found
DEFAULT_PROMPT = """You are a highly specialized data query assistant with expertise in constructing precise generic parser requests. Your primary goal is to generate the most accurate and comprehensive requests that fully satisfy user requirements.

You excel at:
- Analyzing user requirements to determine the exact data needed
- Selecting the most appropriate algorithms for the task
- Constructing precise formulas and indicators
- Ensuring all mandatory fields are correctly populated
- Adding optional fields when they enhance the query results

**YOUR APPROACH:**
1. **ALWAYS START** by identifying and selecting the appropriate algorithm(s) based on the user's needs
2. **THEN** determine the correct labels or compose the proper indicators (for algorithms without predefined labels)
3. **VALIDATE** that your request captures all aspects of what the user is asking for
4. **OPTIMIZE** the request for precision and completeness

**GENERIC PARSER REQUEST FORMAT:**
{
    "partner.code": "oksigen",
    "widget.chart_type": "scatter_plot",
    "start_date": "2024-03-03T00:00",
    "end_date": "2025-03-03T23:59",
    "perimeter.type": "site",
    "perimeter.list": [
        "CAD_CAD_01GEN"
    ],
    "comparison_periods": [
        "2024","2025"
    ],
    "indicators": [
        {
            "code": "K4A30YUL",
            "formula": "{{site#out:site.custom_field_float_3}} / {{site#out:site.custom_field_float_1}}"
        },
        {
            "code": "K4A31SA6",
            "formula": "{{site#out:site.custom_field_float_1}}"
        }
    ],
    "group_by": "site",
    "group_id": [
        "CAD_CAD_01GEN"
    ],
    "data_description": "Data analysis for sites CAD_CAD_01GEN from 2024-03-03 to 2025-03-03, grouped by site with comparison periods 2024-2025"
}

**FIELD DESCRIPTIONS:**

**MANDATORY FIELDS:**
- **partner_code**: Always 'oksigen'
- **perimeter_type**: Type of objects analyzed (always 'site')
- **perimeter_list**: List of site codes for analysis
  - If user doesn't specify sites: use ["all"]
  - To exclude specific sites: start list with "-" followed by sites to exclude
- **group_by**: Result grouping method, and see in the algos description to see the group_by alowed by the algo (if montioned). Options:
  - 'date'
  - 'site'
  - 'site_property' (e.g., typology)
  - 'meter_property'
  - 'statement_property'
  - 'commitment_property'
- **group_id**: Additional grouping identifiers (must be a list) .
  - For group_by='date': ['daily'], ['weekly'], ['monthly'], or ['yearly']
  - For group_by='site': list of site codes
- **start_date**: Analysis start date (format: YYYY-MM-DDTHH:mm)
- **end_date**: Analysis end date (format: YYYY-MM-DDTHH:mm)
- **comparison_periods**: List of comparison periods (e.g., specific years)
- **indicators**: List of indicators, each containing:
  - 'code': Unique identifier
  - 'formula': Calculation formula
- **data_description**: A concise description of the data being requested, including:
  - Time frame (start_date to end_date)
  - Perimeter list (sites being analyzed)
  - Group_by method and group_id
  - Most important indicators or analysis type
  - Example: "Data analysis for sites CAD_CAD_01GEN from 2024-03-03 to 2025-03-03, grouped by site with comparison periods 2024-2025"
- **time_resolution**: Time granularity (use always when group_by='date') and is the same as group_id (but not in list form) 'daily', 'weekly', 'monthly', or 'yearly'

  **OPTIONAL FIELDS:**
- **widget_chart_type**: Visualization type (default: 'histograms_Chart')

**INFORMATION REQUESTS:**
- If you need additional information to construct an accurate request, include a "needed_infos" field in your JSON response.
- The "needed_infos" field should contain a clear description of what information you need.
- Only use this field when essential data is missing and cannot be inferred.
- When using "needed_infos", set all other fields to appropriate default/null values.



Remember: Your first step is ALWAYS to identify the algorithm(s) needed, then build the appropriate indicators or labels to deliver precisely what the user needs."""

def get_llm_model():
    """
    Initializes a chain of LLM models with a specified fallback order.

    The fallback order is: OpenAI -> DeepSeek -> Anthropic -> Google.

    If a model fails to respond (e.g., due to API errors, rate limits), 
    the request is automatically passed to the next model in the chain.

    Model names can be customized via a `model_config.json` file.
    
    Returns:
        A LangChain runnable (ChatModel) with fallbacks configured, or raises
        a RuntimeError if no models could be initialized.
    """
    # Default model names based on your provided snippets
    model_configs = {
        "openai": "gpt-4.1",
        "deepseek": "deepseek-chat",
        "anthropic": "claude-3-5",
        "google": "gemini-2.0-flash",
    }

    # --- Instantiate all potential LLMs ---
    # We set max_retries=0 to ensure that errors are not handled internally
    # by the model's client, allowing the fallback logic to trigger.
    
    initialized_llms = []
    llm_info = []  # Store tuples of (llm, provider_name, model_name)
    
    # 1. OpenAI (Primary)
    try:
        openai_llm = ChatOpenAI(
            model=model_configs["openai"],
            temperature=0,
            max_retries=0 
        )
        initialized_llms.append(openai_llm)
        llm_info.append(("OpenAI", model_configs["openai"]))
        print(f"✅ Initialized OpenAI: {model_configs['openai']}")
    except Exception as e:
        print(f"⚠️  Could not initialize OpenAI model. It will be skipped. Error: {e}")

    # 2. DeepSeek (First Fallback)
    try:
        deepseek_llm = ChatDeepSeek(
            model=model_configs["deepseek"],
            temperature=0,
            max_retries=0
        )
        initialized_llms.append(deepseek_llm)
        llm_info.append(("DeepSeek", model_configs["deepseek"]))
        print(f"✅ Initialized DeepSeek: {model_configs['deepseek']}")
    except Exception as e:
        print(f"⚠️  Could not initialize DeepSeek model. It will be skipped. Error: {e}")

    # 3. Anthropic (Second Fallback)
    try:
        anthropic_llm = ChatAnthropic(
            model=model_configs["anthropic"],
            temperature=0,
            max_retries=0
        )
        initialized_llms.append(anthropic_llm)
        llm_info.append(("Anthropic", model_configs["anthropic"]))
        print(f"✅ Initialized Anthropic: {model_configs['anthropic']}")
    except Exception as e:
        print(f"⚠️  Could not initialize Anthropic model. It will be skipped. Error: {e}")
        
    # 4. Google (Final Fallback)
    try:
        google_llm = ChatGoogleGenerativeAI(
            model=model_configs["google"],
            temperature=0,
            max_retries=0
        )
        initialized_llms.append(google_llm)
        llm_info.append(("Google", model_configs["google"]))
        print(f"✅ Initialized Google: {model_configs['google']}")
    except Exception as e:
        print(f"⚠️  Could not initialize Google model. It will be skipped. Error: {e}")

    # --- Build the fallback chain ---
    if not initialized_llms:
        raise RuntimeError("No LLM providers could be initialized. Please check your API keys (e.g., OPENAI_API_KEY) and configurations.")

    # The first successfully initialized LLM is the primary
    primary_llm = initialized_llms[0]
    primary_info = llm_info[0]
    
    # The rest are the fallbacks
    fallback_llms = initialized_llms[1:]
    fallback_info = llm_info[1:]

    if not fallback_llms:
        print("Note: Only one LLM was initialized. No fallbacks are configured.")
        return primary_llm

    print(f"\n🚀 Using '{primary_info[0]} ({primary_info[1]})' as primary model.")
    print("Fallback chain:")
    for i, (provider, model) in enumerate(fallback_info):
        print(f"  {i+1}. {provider} ({model})")
        
    # Chain the primary model with the list of fallbacks
    model_with_fallbacks = primary_llm.with_fallbacks(fallback_llms)

    return model_with_fallbacks



def update_json_with_sites(json_obj, sites):
    """Update JSON with site information"""
    def process_list(lst, sites):
        if lst == ["all"]:
            return sites
        elif lst and lst[0] == "-":
            return [site for site in sites if site not in lst[1:]]
        return lst

    # Force the correct partner code from environment
    partner_code = os.getenv("DEFAULT_PARTNER", "oksigen")
    json_obj["partner.code"] = partner_code

    # Check and update "group_id"
    if "group_id" in json_obj:
        json_obj["group_id"] = process_list(json_obj["group_id"], sites)

    # Check and update "perimeter.list"
    if "perimeter.list" in json_obj:
        json_obj["perimeter.list"] = process_list(json_obj["perimeter.list"], sites)
    elif "perimeter" in json_obj and "list" in json_obj["perimeter"]:
        json_obj["perimeter"]["list"] = process_list(json_obj["perimeter"]["list"], sites)

    return json_obj

import yaml
from pathlib import Path

# 1. Load once at startup:
_PROMPT_CONFIG_PATH = Path(__file__).parent / "config" / "partner_prompts.yaml"
with open(_PROMPT_CONFIG_PATH, "r") as f:
    PARTNER_CONFIG = yaml.safe_load(f)

def build_partner_prompt(partner_code: str) -> str:
    partner = PARTNER_CONFIG.get(partner_code, {})
    algos = partner.get("algorithms", [])
    if not algos:
        return ""  # fall back to DEFAULT_PROMPT

    lines = ["Here are the available algorithm_ids along with their descriptions:\n"]
    for algo in algos:
        lines.append(f"- **algorithm_id: {algo['id']}**")
        lines.append(f"  Description: {algo['description'].strip()}")
        lines.append(f"  Mode: \"{algo['mode']}\"")

        # Handle both indicator_options and formula fields
        if "indicator_options" in algo:
            lines.append("  indicator options:")
            for code, desc in algo["indicator_options"].items():
                lines.append(f"    - {code} ({desc})")
        elif "formula" in algo:
            lines.append("  formula:")
            formula_data = algo["formula"]
            if isinstance(formula_data, dict):
                for key, value in formula_data.items():
                    lines.append(f"    - {key}: {value}")
            else:
                lines.append(f"    - {formula_data}")

        lines.append("")  # blank line between algos
    text = "\n".join(lines)
    print("==========##################################==========")
    print(f"[PROMPT]: {text}")
    print("==========##################################==========")
    return text


# Simple function-based approach for generating requests
def generate_request(user_request, errors=None, previous_request=None, partner="oksigen"):
    """
    Generate a generic parser request based on the user's requirements

    :param user_request: User's request for data
    :param errors: List of previous errors
    :param previous_request: Previous request JSON
    :param partner: Partner code (defaults to DEFAULT_PARTNER from env)
    """
    # Get partner from environment if not provided
    if partner == "oksigen":  # Only override if using default
        partner = os.getenv("DEFAULT_PARTNER", "oksigen")

    if errors is None:
        errors = []

    from datetime import datetime
    current_date = datetime.now().date()

    # Get the partner-specific prompt or use the default
    # 2. In your generate_request:
    prompt_template = build_partner_prompt(partner)

    # Add user request and current date to the prompt
    prompt =f"""
    How to create an Indicator formula:

    - **Basic Structure:**
    The formula must follow the structure:
    {{algorithm_id#mode:algorithm_indicator_id}}
    Where:
    - **algorithm_id** is a technical identifier (appears between {{ and #).
    - **mode** is either in or out (appears between # and :).
    - **algorithm_indicator_id** comes after the : and before }}.

    - **Aggregation of Multiple Indicators:**
    When using the same algorithm_id, you can aggregate the results from multiple algorithm_indicator_id values. To do this, simply combine them in the same formula using mathematical operators.
    **For example:**
    To aggregate two sensor readings, you can write:
    {{sensor#in:OY4U4HwBCeLaNCYRERoi}} + {{sensor#in:AB2U5HwBfjv8CYREthg}}
    This instructs the system to aggregate the values from both sensor indicators.

    - **Operands:**
    You can include standard mathematical operations in your formulas, such as addition (+), subtraction (-), multiplication (*), and division (/).
    **Examples:**
    {{site#out:site.custom_field_float_3}} / 3 +  {{site#out:site.custom_field_float_1}}
    {{sensor#in:OY4U4HwBCeLaNCYRERoi}} + {{load_curve#out:total-power_supply}}

    Here are the available algorithm_ids along with their descriptions:

    {prompt_template}

    For info:
    - Today is: {current_date}

    The user request is: {user_request}

    Based on the user request, generate a valid JSON request for the generic parser.
    IMPORTANT: Your response must be ONLY the JSON object, nothing else. No explanations, no markdown formatting, just the raw JSON.
    """

    prompt = DEFAULT_PROMPT + prompt

    if errors:
        last_error = str(errors[-1])
        # Only add previous request and error if not a server error or connection error
        if not (("SERVER_ERROR" in last_error) or ("CONNECTION_ERROR" in last_error)):
            if "EMPTY_DETAILS" in last_error:
                prompt += f"""
                The previous request was valid but returned no data (the 'details' field was empty).
                Try to adjust the request parameters (such as group_by, group_id, perimeter.list, indicators, or time frame) to increase the chance of retrieving data.
                In your previous try you generated this request: {previous_request}
                but got this error: {errors[-1]}. Correct this by modifying the request to maximize the chance of retrieving data.
                """
            else:
                prompt += f""" In your previous try you generated this request: {previous_request}
        but got this error: {errors[-1]}. correct this """

    try:
        # Initialize the LLM
        llm = get_llm_model()
        # Get the response
        response = llm.invoke(prompt)

        response = response.content

        # Clean up the response to ensure it's valid JSON
        # Remove any markdown code block markers
        response = response.replace("```json", "").replace("```", "").strip()
        # Try to parse the JSON
        try:
            request_json = json.loads(response)
            return request_json
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON: {str(e)}")
            print(f"Response: {response}")

            # Try to extract JSON from the response if it's embedded in text
            import re
            json_pattern = r'({[\s\S]*})'
            match = re.search(json_pattern, response)

            if match:
                json_str = match.group(1)
                try:
                    request_json = json.loads(json_str)
                    return request_json
                except json.JSONDecodeError:
                    pass

    except Exception as e:
        print(f"Error generating request: {str(e)}")
        return {"error": f"Error generating request: {str(e)}"}

def get_data_by_index(partner, index):
    print(f"Get aggregation of {partner} partner:")
    page = 0
    all_data = []
    try:
        while True:
            page += 1

            headers = {
                "Content-Type": "application/json"
            }
            body = {
                "partner.code": partner,
                "paging": {
                    "page": page,
                    "size": 10_000
                }
            }

            response = httpx.post(f"https://backend-api-stg.energisme.net/parameter/{index}/_search", headers=headers, json=body)
            reply = response.json()

            data = reply.get("data")
            current_count = reply.get("currentCount")
            total_count = reply.get("totalCount")

            if data is not None:
                all_data += data
                print(f"page: {page}, total_count: {total_count}, current_count:{current_count}, data: {len(data)}")

            if current_count == 0 or total_count == current_count:
                break

        return all_data
    except httpx.HTTPError as e:
        print(f"HTTP error occurred: {str(e)}")
        return []
    except Exception as e:
        print(f"Error in partners retrieving: {str(e)}")
        return []

def generate_details_csv(data, file_path, file_name, session_id):
    """
    Generates a CSV file from the "details" part of the input data and uploads it to the sandbox.

    :param data: The input JSON data containing the "details" array.
    :param file_path: Local directory path to temporarily save the CSV before upload.
    :param file_name: Base name of the CSV file (without extension).
    :param session_id: Session ID for the sandbox.
    :return: (local_file_path, sandbox_absolute_path)
    """
    if not session_id:
        raise ValueError("Session ID is required")

    details = data.get('details', [])
    if not details:
        print("No details found in the provided data.")
        return

    # Gather CSV headers
    field_names = sorted({k for d in details for k in d if k != "indicators"})
    indicator_keys = sorted({i['key'] for d in details for i in d.get("indicators", []) if 'key' in i})
    headers = field_names + indicator_keys

    # Prepare data rows
    rows = []
    for detail in details:
        row = {field: detail.get(field, '') for field in field_names}
        for ind in detail.get("indicators", []):
            key = ind.get("key")
            if key in indicator_keys:
                row[key] = ind.get("value", '')
        for key in indicator_keys:
            row.setdefault(key, '')
        rows.append(row)

    os.makedirs(file_path, exist_ok=True)
    local_file_path = os.path.join(file_path, f"{file_name}.csv")

    with open(local_file_path, mode='w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(rows)

    print(f"CSV file saved locally at: {local_file_path}")

    try:
        import asyncio
        from sandbox_client import SandboxClient

        async def upload_to_sandbox():
            client = SandboxClient()
            await client.create_session(session_id)

            # Ensure variable_name doesn't have .csv extension for proper binding in Python
            variable_name = file_name
            if variable_name.endswith('.csv'):
                variable_name = variable_name[:-4]

            result = await client.upload_file(
                session_id=session_id,
                file_path=local_file_path,
                variable_name=variable_name
            )

            if not result.get("success"):
                raise RuntimeError(f"Upload failed: {result.get('error')}")

            print(f"CSV file uploaded to sandbox session {session_id}.")

            # Correct path used by sandbox backend:
            sandbox_file_path = f"/tmp/sessions/{session_id}/files/{file_name}.csv"

            # Verify file existence with actual absolute path
            verification_code = f"""
import os
import pandas as pd

file_path = "{sandbox_file_path}"
if os.path.exists(file_path):
    print(f"File exists in sandbox: {{file_path}}")
    df = pd.read_csv(file_path)
    print(f"Loaded CSV with {{len(df)}} rows and {{len(df.columns)}} columns.")
else:
    print(f"File not found at: {{file_path}}")
"""

            verify_result = await client.execute_code(session_id, verification_code, timeout_seconds=10)
            print(f"[Verification] Output:\n{verify_result.get('output', '')}")

            return local_file_path, sandbox_file_path

        # Run the async function
        return asyncio.run(upload_to_sandbox())

    except Exception as e:
        print(f"Error: {str(e)}")
        raise RuntimeError(str(e))


def process_generic_parser_request(input_json, partner="oksigen", session_id=None):
    # Check for needed_infos field and handle information request
    if "needed_infos" in input_json and input_json["needed_infos"]:
        return {
            "status": "info_needed",
            "needed_infos": input_json["needed_infos"],
            "input_data": [],
            "csv_path": None,
            "data_file_name": None,
            "sandbox_file_name": None,
            "data_description": input_json.get("data_description", "")
        }

    # Validate session_id
    if not session_id:
        raise ValueError("Session ID is required for process_generic_parser_request")

    # Get partner from environment if not provided
    if partner == "oksigen":  # Only override if using default
        partner = os.getenv("DEFAULT_PARTNER", "oksigen")

    # Extract data_description from the request and remove it
    data_description = input_json.pop("data_description", "")

    # Force the correct partner code
    input_json["partner.code"] = partner
    print("********HHHHH**********")
    print(input_json)
    print("***********************")
    # Ensure uploads folder exists
    uploads_folder = "uploads"
    if not os.path.exists(uploads_folder):
        os.makedirs(uploads_folder)

    # First, get all sites for the partner
    all_sites = [item['code'] for item in get_data_by_index(partner, "site")]

    # Update the JSON with site information
    processed_json = update_json_with_sites(input_json.copy(), all_sites)

    # Now proceed with the API request
    url = "http://**********:8065/api/generic-parser"
    error_mapping = {
        "GP_1.1": "MANDATORY_FIELD_EMTPY",
        "GP_1.2": "INVALID_PERIMETER_TYPE",
        "GP_1.10": "INVALID_PERIMETER_LIST",
        "GP_1.3": "INVALID_DATE_FORMAT",
        "GP_1.4": "START_DATE_MUST_BE_BEFORE_END_DATE",
        "GP_1.5": "INVALID_GROUP_BY",
        "GP_1.6": "INVALID_GROUP_ID",
        "GP_1.7": "INVALID_COMPARISON_PERIOD",
    }

    try:
        response = requests.post(url, json=processed_json)

        # Check if there's a response at all
        if response is None:
            print("No response received from server")
            return "CONNECTION_ERROR"

        response.raise_for_status()  # Raises HTTPError for 4xx/5xx status codes

        # Check if there's details in the response
        if not response.content:
            print("Empty response received from server")
            return "INVALID_REQUEST"


        # Attempt to parse JSON response
        try:
            response_data = response.json()
        except requests.exceptions.JSONDecodeError as e:
            print("INVALID_REQUEST")
            return "INVALID_REQUEST"

        print("********response_data**********")
        print(response_data)
        print("********response_data**********")
        # Check if the response contains an error code
        error_code = response_data.get("error")
        if error_code in error_mapping:
            error_message = error_mapping[error_code]
            print(error_message)
            return error_message
        elif error_code is not None:  # Unrecognized error code
            print("INVALID_REQUEST")
            return "INVALID_REQUEST"
        # Check if details is empty
        elif not response_data.get("details"):
            print("EMPTY_DETAILS: The 'details' field is empty in the response.")
            return "EMPTY_DETAILS"
        else:  # Valid response with results
            # Save the response data as CSV directly
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"response_{timestamp}"

            # Generate CSV from the response data and upload to sandbox
            _, sandbox_file_name = generate_details_csv(response_data, uploads_folder, csv_filename, session_id)

            print(f"Response data saved as CSV to {uploads_folder}/{csv_filename}.csv")
            print(f"File in sandbox: {sandbox_file_name}")

            # Return both the response data and the paths to the CSV
            return {
                "input_data": response_data,
                "csv_path": f"{uploads_folder}/{csv_filename}.csv",
                "data_file_name": csv_filename,
                "sandbox_file_name": sandbox_file_name,
                "data_description": data_description  # Include the extracted data_description
            }

    except requests.exceptions.HTTPError as e:
        # Handle HTTP errors (4xx, 5xx)
        if e.response.status_code in [400, 500]:
            try:
                error_data = e.response.json()
                error_code = error_data.get("error")
                if error_code in error_mapping:
                    error_message = error_mapping[error_code]
                    print(error_message)
                    return error_message
                else:
                    print("INVALID_REQUEST")
                    return "INVALID_REQUEST"
            except requests.exceptions.JSONDecodeError:
                print("INVALID_REQUEST")
                return "INVALID_REQUEST"
        else:
            # For other HTTP errors, return the error from the server
            error_message = f"SERVER_ERROR: HTTP error {e.response.status_code}"
            print(error_message)
            return {"status": "SERVER_ERROR", "detail": f"HTTP error: {e}", "status_code": e.response.status_code}
    except requests.exceptions.RequestException as e:
        # Handle network errors, timeouts, etc.
        error_message = f"CONNECTION_ERROR: {str(e)}"
        print(error_message)
        return {"status": "CONNECTION_ERROR", "detail": f"Request failed: {str(e)}"}

def get_data(user_request, errors=None, previous_request=None, session_id=None, partner="oksigen"):
    """
    Main method to generate request and get data

    :param user_request: User's request for data
    :param errors: List of previous errors
    :param previous_request: Previous request JSON
    :param session_id: Optional session ID for sandbox integration
    :param partner: Partner code to use for the request (default: "oksigen")
    """
    if errors is None:
        errors = []

    # Generate the request
    request_json = generate_request(user_request, errors, previous_request)
    print("_____request_json_____")
    print(request_json)
    print("_____________")

    # If there was an error generating the request
    if "error" in request_json:
        return request_json

    # Process the request
    result = process_generic_parser_request(request_json, partner=partner, session_id=session_id)

    # Check if we got a proper response with a CSV path
    if isinstance(result, dict) and "csv_path" in result:
        # Create InputData object with the retrieved data
        input_data = {
            "file_path": result["sandbox_file_name"],
            "file_name": os.path.basename(result["csv_path"]),
            "description": f"Data retrieved from generic parser for request: {user_request}",
        }

        return {
            "status": "success",
            "data": result["input_data"],
            "input_data": input_data,
            "csv_path": result["csv_path"],
            "data_file_name": result["data_file_name"],
            "sandbox_file_name": result.get("sandbox_file_name", ""),
            "data_description": result.get("data_description", "")
        }

    return result

# Define the state for the Generic Parser Agent graph
from typing import TypedDict, List, Any, Annotated
import operator

def add_unique_optimized(left: List[Any], right: List[Any]) -> List[Any]:
    """Add items from right to left with O(1) duplicate checking."""
    result = left.copy()
    
    # Helper function to check if two items are equal
    def items_equal(item1, item2):
        if isinstance(item1, dict) and isinstance(item2, dict):
            return item1 == item2
        return item1 == item2
    
    # For each item in right list
    for item in right:
        # Check if item already exists in result
        if not any(items_equal(item, existing_item) for existing_item in result):
            result.append(item)
    
    return result

class GenericParserState(TypedDict):
    user_request: Annotated[List[str], add_unique_optimized]
    generated_request: Annotated[List[Any], add_unique_optimized]
    response_data: Annotated[List[Any], add_unique_optimized]
    requests_error: Annotated[List[str], operator.add]
    input_data: Annotated[List[InputData], add_unique_optimized]
    session_id: str  # Added for sandbox integration
    partner: str  # Added for partner customization
    needed_infos: str  # Descriptions of additional information needed by the data retriever

# Define the nodes for the Generic Parser graph
def generate_request_node(state: GenericParserState) -> Dict[str, Any]:
    user_request = state["user_request"][-1] if state["user_request"] else ""
    previous_request = state["generated_request"][-1] if state["generated_request"] else None
    errors = state["requests_error"]
    partner = state.get("partner", "oksigen")  # Default to "oksigen" if not specified

    request_json = generate_request(user_request, errors, previous_request, partner=partner)
    print(request_json)

    if "error" in request_json:
        state["requests_error"] = state["requests_error"] + [request_json["error"]]
        return state

    state["generated_request"] = state["generated_request"] + [request_json]
    return state

def process_request_node(state: GenericParserState) -> Dict[str, Any]:
    """Process the generated request and get data"""

    # Ensure that there's a generated request before proceeding
    if not state.get("generated_request") or len(state["generated_request"]) == 0:
        state["requests_error"] = state["requests_error"] + ["No generated request available."]
        return state

    print("***********************")
    print(state["generated_request"][-1])
    print("***********************")
    if len(state["requests_error"]) > 0:
        print("++++++++++++++Errors+++++++++++++++")
        print(state["requests_error"][-1])
        print("+++++++++++++++++++++++++++++++++++")

    request_json = state["generated_request"][-1]

    # Get session_id and partner from state
    session_id = state.get("session_id")
    partner = state.get("partner", "oksigen")  # Default to "oksigen" if not specified

    try:
        response = process_generic_parser_request(request_json, partner=partner, session_id=session_id)

        # Handle info_needed response
        if isinstance(response, dict) and response.get("status") == "info_needed":
            state["needed_infos"] = response.get("needed_infos", "")
            state["input_data"] = []
            return state

        # Check if there was an error processing the request
        if isinstance(response, str) and response in [
            "CONNECTION_ERROR", "INVALID_REQUEST", "MANDATORY_FIELD_EMTPY",
            "INVALID_PERIMETER_TYPE", "INVALID_PERIMETER_LIST",
            "INVALID_DATE_FORMAT", "START_DATE_MUST_BE_BEFORE_END_DATE",
            "INVALID_GROUP_BY", "INVALID_GROUP_ID", "INVALID_COMPARISON_PERIOD",
            "EMPTY_DETAILS"
        ]:
            state["requests_error"] = state["requests_error"] + [response]
            return state

        if isinstance(response, dict) and "status" in response and response["status"] in ["SERVER_ERROR", "CONNECTION_ERROR"]:
            state["requests_error"] = state["requests_error"] + [f"{response['status']}: {response.get('detail', '')}"]
            return state

        # If we get here, the request was successful
        if "response_data" not in state:
            state["response_data"] = []
        state["response_data"] = state["response_data"] + [response]

        # If we have input_data in the response, add it to the state
        if isinstance(response, dict) and "input_data" in response:
            if "input_data" not in state:
                state["input_data"] = []

            state["input_data"] = state["input_data"] + [
                InputData(
                    variable_name=response["data_file_name"],
                    data_path=response["csv_path"],
                    data_description=response.get("data_description", state["user_request"][-1] if state["user_request"] else ""),
                    sandbox_file_name=response.get("sandbox_file_name", "")
                )]
        return state
    except Exception as e:
        state["requests_error"] = state["requests_error"] + [f"Error processing request: {str(e)}"]
        return state

# Create the Generic Parser graph
def create_generic_parser_graph():
    # Build the graph
    builder = StateGraph(GenericParserState)

    # Add nodes
    builder.add_node("generate_request", generate_request_node)
    builder.add_node("process_request", process_request_node)

    # Add starting edge
    builder.add_edge(START, "generate_request")

    # Add direct edge from generate_request to process_request
    # Every request must be processed
    builder.add_edge("generate_request", "process_request")

    # Add conditional edge based on errors after processing
    def handle_after_processing(state: GenericParserState) -> str:
        errors = state["requests_error"]
        # If we've had 4 or more errors, end the graph (to not loop indefinitely)
        if len(errors) >= 4:
            return END
        # If there's at least one error, retry generation
        elif len(errors) > 0:
            return "generate_request"
        # If no errors, end successfully
        else:
            return END

    # Use conditional edges from process_request
    builder.add_conditional_edges("process_request", handle_after_processing)

    # Compile the graph
    return builder.compile()

# Make sure the initial state has the correct structure when starting the graph
def run_generic_parser_graph(user_request: str, session_id: str = None, partner: str = "oksigen"):
    """
    Run the generic parser graph with the given user request and session ID.

    :param user_request: User's request for data
    :param session_id: Session ID for sandbox integration
    :param partner: Partner code to use for the request (default: "oksigen")
    :return: Result of the graph execution
    """
    # Initialize the graph
    graph = create_generic_parser_graph()

    # Create initial state with empty lists for all fields
    initial_state = {
        "user_request": [user_request],
        "generated_request": [],
        "response_data": [],
        "requests_error": [],
        "input_data": [],
        "session_id": session_id,  # Add session_id to the state
        "partner": partner,  # Add partner to the state
        "needed_infos": ""  # Initialize needed_infos
    }

    # Create config with session_id if provided
    config = {}
    if session_id:
        config = {"session_id": session_id}

    # Run the graph with the initial state and config
    result = graph.invoke(initial_state, config)

    return result
