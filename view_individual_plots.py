#!/usr/bin/env python3
"""
Script to view individual plots for a given session ID.
Creates separate HTML files for each plot and opens them in the browser.

Usage:
    python view_individual_plots.py <session_id>
    
Example:
    python view_individual_plots.py 07d7ea02-ca2b-4874-9760-ce881ba454ab
"""

import sys
import os
import pickle
import asyncio
import argparse
from pathlib import Path
from typing import List, Dict, Any
import webbrowser
import tempfile
import time

# Add the current directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from sandbox_client import SandboxClient
    from plot_template import apply_company_style
    import plotly.graph_objects as go
    import plotly.offline as pyo
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure you have plotly installed: pip install plotly")
    sys.exit(1)


class IndividualPlotViewer:
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.sandbox_client = SandboxClient()
        self.output_dir = Path(f"session_plots_{session_id[:8]}")
    
    async def fetch_session_info(self) -> Dict[str, Any]:
        """Fetch session information to get available plots."""
        try:
            session_info = await self.sandbox_client.get_session_info(self.session_id)
            if "error" in session_info:
                raise Exception(f"Session error: {session_info['error']}")
            return session_info
        except Exception as e:
            raise Exception(f"Failed to fetch session info: {str(e)}")
    
    async def get_plot_list(self) -> List[str]:
        """Get list of plot files for the session."""
        session_info = await self.fetch_session_info()
        
        # Get plots from session info
        plots = session_info.get("plots", [])
        
        # Filter out plots that start with 'plotly_plotly'
        filtered_plots = [
            plot for plot in plots 
            if not plot.startswith("plotly_plotly")
            and plot.endswith(".pickle")
            and ("plotly" in plot.lower() or "fig" in plot.lower())
        ]
        
        print(f"Found {len(plots)} total plots")
        print(f"Filtered plots (excluding plotly_plotly*): {len(filtered_plots)}")
        
        if filtered_plots:
            print("Available plots:")
            for i, plot in enumerate(filtered_plots, 1):
                print(f"  {i}. {plot}")
        
        return filtered_plots
    
    async def download_plot(self, plot_name: str) -> go.Figure:
        """Download and deserialize a plot."""
        try:
            print(f"Downloading plot: {plot_name}")
            plot_bytes = await self.sandbox_client.download_plot(
                session_id=self.session_id,
                plot_name=plot_name
            )
            
            if not plot_bytes:
                raise Exception(f"No data received for plot {plot_name}")
            
            # Deserialize the pickle
            fig = pickle.loads(plot_bytes)
            
            if not isinstance(fig, go.Figure):
                raise Exception(f"Plot {plot_name} is not a Plotly Figure object")
            
            # Apply company styling
            fig = apply_company_style(fig)
            
            # Update title to include plot name
            current_title = fig.layout.title.text if fig.layout.title else ""
            new_title = f"{plot_name.replace('.pickle', '').replace('_', ' ').title()}"
            if current_title and current_title != new_title:
                new_title = f"{new_title} - {current_title}"
            
            fig.update_layout(
                title=new_title,
                title_x=0.5
            )
            
            return fig
            
        except Exception as e:
            print(f"Error downloading plot {plot_name}: {str(e)}")
            return None
    
    def create_output_directory(self):
        """Create output directory for HTML files."""
        self.output_dir.mkdir(exist_ok=True)
        print(f"Output directory: {self.output_dir.absolute()}")
    
    def save_plot_html(self, fig: go.Figure, plot_name: str) -> str:
        """Save a single plot as HTML file."""
        if not fig:
            return None
        
        # Create filename
        safe_name = plot_name.replace(".pickle", "").replace("/", "_").replace("\\", "_")
        html_file = self.output_dir / f"{safe_name}.html"
        
        # Generate HTML content
        html_content = pyo.plot(
            fig, 
            output_type='div', 
            include_plotlyjs=True,
            config={'displayModeBar': True, 'responsive': True}
        )
        
        # Create full HTML document
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{safe_name} - Session {self.session_id[:8]}...</title>
            <meta charset="utf-8">
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 20px;
                    padding: 20px;
                    background-color: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                .plot-container {{
                    background-color: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    padding: 20px;
                }}
                .navigation {{
                    text-align: center;
                    margin-top: 20px;
                    padding: 10px;
                    background-color: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                .nav-button {{
                    display: inline-block;
                    margin: 0 10px;
                    padding: 10px 20px;
                    background-color: #007bff;
                    color: white;
                    text-decoration: none;
                    border-radius: 4px;
                    transition: background-color 0.3s;
                }}
                .nav-button:hover {{
                    background-color: #0056b3;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{safe_name}</h1>
                <p><strong>Session ID:</strong> {self.session_id}</p>
                <p><strong>Plot File:</strong> {plot_name}</p>
            </div>
            <div class="plot-container">
                {html_content}
            </div>
            <div class="navigation">
                <a href="index.html" class="nav-button">← Back to Index</a>
                <a href="#" onclick="window.close()" class="nav-button">Close Window</a>
            </div>
        </body>
        </html>
        """
        
        # Write to file
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(full_html)
        
        print(f"Saved: {html_file}")
        return str(html_file)
    
    def create_index_html(self, plot_files: List[Dict[str, str]]):
        """Create an index HTML file with links to all plots."""
        index_file = self.output_dir / "index.html"
        
        plot_links = ""
        for plot_info in plot_files:
            plot_links += f"""
                <div class="plot-item">
                    <h3>{plot_info['display_name']}</h3>
                    <p><strong>File:</strong> {plot_info['original_name']}</p>
                    <a href="{plot_info['html_file']}" class="plot-link" target="_blank">View Plot</a>
                </div>
            """
        
        index_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Session Plots Index - {self.session_id[:8]}...</title>
            <meta charset="utf-8">
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    padding: 30px;
                    background-color: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                .plots-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                }}
                .plot-item {{
                    background-color: white;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    text-align: center;
                }}
                .plot-link {{
                    display: inline-block;
                    margin-top: 10px;
                    padding: 10px 20px;
                    background-color: #28a745;
                    color: white;
                    text-decoration: none;
                    border-radius: 4px;
                    transition: background-color 0.3s;
                }}
                .plot-link:hover {{
                    background-color: #1e7e34;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding: 20px;
                    background-color: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    color: #666;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Session Plots Index</h1>
                <p><strong>Session ID:</strong> {self.session_id}</p>
                <p><strong>Number of Plots:</strong> {len(plot_files)}</p>
                <p><strong>Generated:</strong> {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="plots-grid">
                {plot_links}
            </div>
            
            <div class="footer">
                <p>Click on any plot link to view it in a new tab.</p>
                <p>These HTML files are saved locally and can be shared or archived.</p>
            </div>
        </body>
        </html>
        """
        
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write(index_html)
        
        print(f"Index file created: {index_file}")
        return str(index_file)
    
    async def view_all_plots(self):
        """Main method to fetch and save all plots as individual HTML files."""
        try:
            print(f"Fetching plots for session: {self.session_id}")
            
            # Get plot list
            plot_names = await self.get_plot_list()
            
            if not plot_names:
                print("No plots found to display.")
                return
            
            # Create output directory
            self.create_output_directory()
            
            # Process each plot
            plot_files = []
            
            for plot_name in plot_names:
                print(f"\nProcessing: {plot_name}")
                
                # Download and process plot
                fig = await self.download_plot(plot_name)
                
                if fig:
                    # Save as HTML
                    html_file = self.save_plot_html(fig, plot_name)
                    
                    if html_file:
                        plot_files.append({
                            'original_name': plot_name,
                            'display_name': plot_name.replace('.pickle', '').replace('_', ' ').title(),
                            'html_file': Path(html_file).name  # Just the filename for relative links
                        })
                else:
                    print(f"Failed to process: {plot_name}")
            
            if plot_files:
                # Create index file
                index_file = self.create_index_html(plot_files)
                
                # Open index in browser
                webbrowser.open(f'file://{Path(index_file).absolute()}')
                
                print(f"\n✅ Successfully processed {len(plot_files)} plots!")
                print(f"📁 Output directory: {self.output_dir.absolute()}")
                print(f"🌐 Index file opened in browser: {index_file}")
                print(f"\nYou can:")
                print(f"  - Browse plots using the index page")
                print(f"  - Share the entire '{self.output_dir.name}' folder")
                print(f"  - Archive the HTML files for later viewing")
            else:
                print("No plots were successfully processed.")
                
        except Exception as e:
            print(f"Error viewing plots: {str(e)}")
            import traceback
            traceback.print_exc()


async def main():
    parser = argparse.ArgumentParser(
        description="View individual plots for a given session ID",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python view_individual_plots.py 07d7ea02-ca2b-4874-9760-ce881ba454ab
  python view_individual_plots.py abc123def456 --list-only
        """
    )
    
    parser.add_argument(
        "session_id",
        help="The session ID to fetch plots for"
    )
    
    parser.add_argument(
        "--list-only",
        action="store_true",
        help="Only list available plots without downloading them"
    )
    
    args = parser.parse_args()
    
    if not args.session_id:
        print("Error: Session ID is required")
        parser.print_help()
        sys.exit(1)
    
    viewer = IndividualPlotViewer(args.session_id)
    
    if args.list_only:
        try:
            await viewer.get_plot_list()
        except Exception as e:
            print(f"Error listing plots: {str(e)}")
    else:
        await viewer.view_all_plots()


if __name__ == "__main__":
    asyncio.run(main())
