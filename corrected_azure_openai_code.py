"""
Corrected version of the original Azure OpenAI initialization code
"""

import os
from langchain_openai import AzureChatOpenAI

# Initialize lists to store models and info
initialized_llms = []
llm_info = []

# CORRECTED VERSION - Fixed the issues in the original code:

# 1. Fixed: Use correct environment variable for endpoint
os.environ["AZURE_OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
os.environ["AZURE_OPENAI_ENDPOINT"] = os.getenv("AZURE_OPENAI_ENDPOINT")  # Fixed: was using OPENAI_API_KEY
os.environ["OPENAI_API_VERSION"] = "2024-02-15-preview"  # Fixed: using valid API version

# 2. Fixed: Use direct AzureChatOpenAI instead of init_chat_model for better compatibility
model = AzureChatOpenAI(
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    api_key=os.getenv("OPENAI_API_KEY"),
    api_version="2024-02-15-preview",
    azure_deployment="gpt-4",  # Fixed: using standard deployment name
    temperature=0,
    max_retries=0
)

initialized_llms.append(model)
llm_info.append(("Azure OpenAI", "gpt-4"))  # Fixed: using standard model name
print(f"✅ Initialized Azure OpenAI: gpt-4")

# Alternative approach using init_chat_model (if you prefer):
"""
from langchain.chat_models import init_chat_model

model = init_chat_model(
    "gpt-4",  # Fixed: using standard model name
    model_provider="azure_openai",  # Added: explicit provider
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    api_key=os.getenv("OPENAI_API_KEY"),
    api_version="2024-02-15-preview",
    azure_deployment="gpt-4",  # Fixed: using standard deployment name
    temperature=0,
    max_retries=0
)
"""
