"""
Corrected version of the original Azure OpenAI initialization code using init_chat_model
"""

import os
from langchain.chat_models import init_chat_model

# Initialize lists to store models and info
initialized_llms = []
llm_info = []

# CORRECTED VERSION - Fixed the issues in the original code:

# 1. Fixed: Use correct environment variable for endpoint
os.environ["AZURE_OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
os.environ["AZURE_OPENAI_ENDPOINT"] = os.getenv("AZURE_OPENAI_ENDPOINT")  # Fixed: was using OPENAI_API_KEY
os.environ["OPENAI_API_VERSION"] = "2024-02-15-preview"  # Fixed: using valid API version

# 2. Fixed: Use init_chat_model with correct parameters
model = init_chat_model(
    "gpt-4",  # Fixed: using standard model name instead of "azure_openai:gpt-4.1"
    model_provider="azure_openai",  # Added: explicit provider specification
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),  # Added: explicit endpoint parameter
    api_key=os.getenv("OPENAI_API_KEY"),  # Added: explicit API key parameter
    api_version="2024-02-15-preview",  # Added: explicit API version parameter
    azure_deployment="gpt-4",  # Fixed: using standard deployment name instead of "gpt-4.1"
    temperature=0,
    max_retries=0
)

initialized_llms.append(model)
llm_info.append(("Azure OpenAI", "gpt-4"))  # Fixed: using standard model name
print(f"✅ Initialized Azure OpenAI: gpt-4")

# ORIGINAL CODE (with issues commented):
"""
# ISSUES IN ORIGINAL CODE:
os.environ["AZURE_OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
os.environ["AZURE_OPENAI_ENDPOINT"] = os.getenv("OPENAI_API_KEY")  # ❌ Wrong! Should be AZURE_OPENAI_ENDPOINT
os.environ["OPENAI_API_VERSION"] = "2025-03-01-preview"  # ❌ Invalid future date

model = init_chat_model(
    "azure_openai:gpt-4.1",  # ❌ Non-standard format
    azure_deployment="gpt-4.1",  # ❌ Non-standard deployment name
    temperature=0,
    max_retries=0
    # ❌ Missing: model_provider, azure_endpoint, api_key, api_version parameters
)
"""
