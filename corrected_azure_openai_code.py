"""
Corrected version of the original Azure OpenAI initialization code using init_chat_model
"""

import os
from langchain.chat_models import init_chat_model

# Initialize lists to store models and info
initialized_llms = []
llm_info = []

# CORRECTED VERSION - Using your exact format:

os.environ["AZURE_OPENAI_API_KEY"] = "..."
os.environ["AZURE_OPENAI_ENDPOINT"] = "..."
os.environ["OPENAI_API_VERSION"] = "2025-03-01-preview"

llm = init_chat_model(
    "azure_openai:gpt-4.1",
    azure_deployment=os.environ["AZURE_OPENAI_DEPLOYMENT_NAME"],
)

initialized_llms.append(llm)
llm_info.append(("Azure OpenAI", "gpt-4.1"))
print(f"✅ Initialized Azure OpenAI: gpt-4.1")

# ORIGINAL CODE (with issues commented):
"""
# ISSUES IN ORIGINAL CODE:
os.environ["AZURE_OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
os.environ["AZURE_OPENAI_ENDPOINT"] = os.getenv("OPENAI_API_KEY")  # ❌ Wrong! Should be AZURE_OPENAI_ENDPOINT
os.environ["OPENAI_API_VERSION"] = "2025-03-01-preview"  # ❌ Invalid future date

model = init_chat_model(
    "azure_openai:gpt-4.1",  # ❌ Non-standard format
    azure_deployment="gpt-4.1",  # ❌ Non-standard deployment name
    temperature=0,
    max_retries=0
    # ❌ Missing: model_provider, azure_endpoint, api_key, api_version parameters
)
"""
