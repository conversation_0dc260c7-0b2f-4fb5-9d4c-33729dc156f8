"""
Corrected version of the original Azure OpenAI initialization code using AzureChatOpenAI
"""

from langchain_openai import AzureChatOpenAI

# Initialize lists to store models and info
initialized_llms = []
llm_info = []

# CORRECTED VERSION - Using AzureChatOpenAI as requested:

llm = AzureChatOpenAI(
    azure_deployment="gpt-35-turbo",  # or your deployment
    api_version="2023-06-01-preview",  # or your api version
    temperature=0,
    max_tokens=None,
    timeout=None,
    max_retries=2,
    # other params...
)

initialized_llms.append(llm)
llm_info.append(("Azure OpenAI", "gpt-35-turbo"))
print(f"✅ Initialized Azure OpenAI: gpt-35-turbo")

# ORIGINAL CODE (with issues commented):
"""
# ISSUES IN ORIGINAL CODE:
os.environ["AZURE_OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
os.environ["AZURE_OPENAI_ENDPOINT"] = os.getenv("OPENAI_API_KEY")  # ❌ Wrong! Should be AZURE_OPENAI_ENDPOINT
os.environ["OPENAI_API_VERSION"] = "2025-03-01-preview"  # ❌ Invalid future date

model = init_chat_model(
    "azure_openai:gpt-4.1",  # ❌ Non-standard format
    azure_deployment="gpt-4.1",  # ❌ Non-standard deployment name
    temperature=0,
    max_retries=0
    # ❌ Missing: model_provider, azure_endpoint, api_key, api_version parameters
)
"""
