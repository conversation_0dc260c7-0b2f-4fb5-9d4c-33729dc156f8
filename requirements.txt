agno==1.1.8
mlflow==3.1.0rc0
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiohttp-retry==2.8.3
aiosignal==1.3.1
alembic==1.14.1
altair==5.5.0
amqp==5.2.0
annotated-types==0.6.0
anthropic==0.51.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
appdirs==1.4.4
apt-xapian-index==0.49
asttokens==2.2.1
async-timeout==4.0.3
asyncer==0.0.7
asyncpg==0.30.0
asyncssh==2.14.1
atpublic==4.0
attrs==23.1.0
autogen-agentchat==0.4.7
autogen-core==0.4.7
autogen-ext==0.4.7
autogenstudio==0.4.1.11
Automat==20.2.0
azure-ai-documentintelligence==1.0.0
azure-ai-inference==1.0.0b9
azure-core==1.32.0
azure-identity==1.20.0
Babel==2.8.0
backcall==0.2.0
backoff==2.2.1
bcrypt==3.2.0
beautifulsoup4==4.10.0
beniget==0.4.1
bidict==0.23.1
billiard==4.2.0
blinker==1.4
Brlapi==0.8.3
Brotli==1.0.9
cachetools==5.5.2
celery==5.3.6
certifi==2023.5.7
cffi==1.16.0
chainlit==2.5.5
chardet==4.0.0
charset-normalizer==2.0.12
chevron==0.14.0
click==8.1.7
click-didyoumean==0.3.0
click-plugins==1.1.1
click-repl==0.3.0
cobble==0.1.4
colorama==0.4.6
comm==0.1.3
command-not-found==0.3
commonmark==0.9.1
configobj==5.0.8
constantly==15.1.0
cryptography==41.0.7
cssselect==1.1.0
cupshelpers==1.0
cycler==0.11.0
dacite==1.6.0
dagshub==0.3.9
dataclasses-json==0.6.7
dbus-python==1.2.18
debugpy==1.6.7
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.18
dictdiffer==0.9.0
diskcache==5.6.3
distlib==0.3.7
distro==1.7.0
distro-info==1.1+ubuntu0.2
dnspython==2.7.0
docker==5.0.3
docker-compose==1.29.2
dockerpty==0.4.1
docopt==0.6.2
docstring_parser==0.16
dpath==2.1.6
dulwich==0.21.6
dvc==3.30.1
dvc-data==2.22.1
dvc-http==2.30.2
dvc-objects==1.2.0
dvc-render==0.6.0
dvc-studio-client==0.17.0
dvc-task==0.3.0
email_validator==2.2.0
entrypoints==0.4
et_xmlfile==2.0.0
exceptiongroup==1.2.0
executing==1.2.0
fastapi==0.115.11
fastapi-cli==0.0.7
fastds==0.6.0
filelock==3.12.2
filetype==1.2.0
flatten-dict==0.4.2
flufl.lock==7.1.1
fonttools==4.29.1
frozenlist==1.4.0
fs==2.4.12
fsspec==2023.10.0
funcy==2.0
fuse-python==1.0.2
fusepy==3.0.1
future==0.18.2
gast==0.5.2
GDAL==3.4.1
gitdb==4.0.11
GitPython==3.1.40
google-ai-generativelanguage==0.6.18
google-api-core==2.24.2
google-auth==2.38.0
google-genai==1.3.0
googleapis-common-protos==1.70.0
gpg==1.16.0
gql==3.4.1
grandalf==0.8
graphql-core==3.2.3
greenlet==3.1.1
grpcio==1.71.0
grpcio-status==1.71.0
gto==1.5.0
gyp==0.1
h11==0.14.0
h2==4.1.0
hpack==4.0.0
html2text==2024.2.26
html5lib==1.1
httpcore==1.0.7
httplib2==0.20.2
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.31.2
humanize==3.12.0
hydra-core==1.3.2
hyperframe==6.0.0
hyperlink==21.0.0
idna==3.3
importlib_metadata==8.5.0
incremental==21.3.0
inflection==0.5.1
iniconfig==2.1.0
ipykernel==6.24.0
ipython==8.14.0
ipython_genutils==0.2.0
isodate==0.7.2
itemadapter==0.4.0
itemloaders==1.0.4
iterative-telemetry==0.0.8
jedi==0.18.2
jeepney==0.7.1
Jinja2==3.1.5
jiter==0.8.2
jmespath==0.10.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
jupyter_client==8.3.0
jupyter_core==5.3.1
keyring==23.5.0
kiwisolver==1.3.2
kombu==5.3.4
langchain==0.3.25
langchain-anthropic==0.3.13
langchain-community==0.3.24
langchain-core==0.3.60
langchain-deepseek==0.1.3
langchain-experimental==0.3.4
langchain-google-genai==2.1.4
langchain-litellm==0.2.1
langchain-openai==0.3.17
langchain-text-splitters==0.3.8
langgraph==0.4.5
langgraph-checkpoint==2.0.26
langgraph-checkpoint-postgres==2.0.21
langgraph-prebuilt==0.1.8
langgraph-sdk==0.1.69
langsmith==0.3.42
language-selector==0.1
launchpadlib==1.10.16
Lazify==0.4.0
lazr.restfulclient==0.14.4
lazr.uri==1.0.6
litellm==1.69.3
literalai==0.1.201
loguru==0.7.3
louis==3.20.0
lxml==4.8.0
lz4==3.1.3+dfsg
Mako==1.3.9
mammoth==1.9.0
markdown-it-py==3.0.0
markdownify==1.0.0
markitdown==0.0.1a5
MarkupSafe==2.0.1
marshmallow==3.20.1
matplotlib==3.5.1
matplotlib-inline==0.1.6
mcp==1.9.0
mdurl==0.1.2
monotonic==1.6
more-itertools==8.10.0
mpmath==0.0.0
msal==1.31.1
msal-extensions==1.2.0
multidict==6.0.4
mypy-extensions==1.0.0
narwhals==1.29.0
nbformat==5.1.3
nest-asyncio==1.6.0
netifaces==0.11.0
networkx==3.2.1
numpy==1.26.2
oauthlib==3.2.0
olefile==0.46
omegaconf==2.3.0
onboard==1.4.1
openai==1.75.0
openpyxl==3.1.5
opentelemetry-api==1.31.1
opentelemetry-exporter-otlp==1.31.1
opentelemetry-exporter-otlp-proto-common==1.31.1
opentelemetry-exporter-otlp-proto-grpc==1.31.1
opentelemetry-exporter-otlp-proto-http==1.31.1
opentelemetry-instrumentation==0.52b1
opentelemetry-instrumentation-alephalpha==0.40.5
opentelemetry-instrumentation-anthropic==0.40.5
opentelemetry-instrumentation-bedrock==0.40.5
opentelemetry-instrumentation-chromadb==0.40.5
opentelemetry-instrumentation-cohere==0.40.5
opentelemetry-instrumentation-crewai==0.40.5
opentelemetry-instrumentation-google-generativeai==0.40.5
opentelemetry-instrumentation-groq==0.40.5
opentelemetry-instrumentation-haystack==0.40.5
opentelemetry-instrumentation-lancedb==0.40.5
opentelemetry-instrumentation-langchain==0.40.5
opentelemetry-instrumentation-llamaindex==0.40.5
opentelemetry-instrumentation-logging==0.52b1
opentelemetry-instrumentation-marqo==0.40.5
opentelemetry-instrumentation-mcp==0.40.5
opentelemetry-instrumentation-milvus==0.40.5
opentelemetry-instrumentation-mistralai==0.40.5
opentelemetry-instrumentation-ollama==0.40.5
opentelemetry-instrumentation-openai==0.40.5
opentelemetry-instrumentation-pinecone==0.40.5
opentelemetry-instrumentation-qdrant==0.40.5
opentelemetry-instrumentation-replicate==0.40.5
opentelemetry-instrumentation-requests==0.52b1
opentelemetry-instrumentation-sagemaker==0.40.5
opentelemetry-instrumentation-sqlalchemy==0.52b1
opentelemetry-instrumentation-threading==0.52b1
opentelemetry-instrumentation-together==0.40.5
opentelemetry-instrumentation-transformers==0.40.5
opentelemetry-instrumentation-urllib3==0.52b1
opentelemetry-instrumentation-vertexai==0.40.5
opentelemetry-instrumentation-watsonx==0.40.5
opentelemetry-instrumentation-weaviate==0.40.5
opentelemetry-proto==1.31.1
opentelemetry-sdk==1.31.1
opentelemetry-semantic-conventions==0.52b1
opentelemetry-semantic-conventions-ai==0.4.8
opentelemetry-util-http==0.52b1
orjson==3.10.18
ormsgpack==1.9.1
OWSLib==0.25.0
packaging==24.2
pandas==2.1.3
parsel==1.6.0
parso==0.8.3
pathspec==0.11.2
pathvalidate==3.0.0
pbr==5.8.0
pdfminer.six==20240706
pexpect==4.8.0
pickleshare==0.7.5
pillow==11.1.0
pipenv==2023.7.11
platformdirs==3.9.1
playwright==1.50.0
plotly==6.1.2
pluggy==1.5.0
ply==3.11
portalocker==2.10.1
posthog==3.25.0
progress==1.6
prompt-toolkit==3.0.39
propcache==0.3.1
Protego==0.2.1
proto-plus==1.26.1
protobuf==5.29.3
psutil==5.9.5
psycopg==3.2.5
psycopg-pool==3.2.6
psycopg2==2.9.2
ptyprocess==0.7.0
pure-eval==0.2.2
puremagic==1.28
pyarrow==19.0.1
pyasn1==0.4.8
pyasn1-modules==0.2.1
pycairo==1.20.1
pycparser==2.21
pycups==2.0.1
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
pydeck==0.9.1
PyDispatcher==2.0.5
pydot==1.4.2
pydub==0.25.1
pyee==12.1.1
pygame==2.5.2
pygit2==1.13.3
Pygments==2.15.1
PyGObject==3.42.1
pygtrie==2.5.0
PyHamcrest==2.0.2
PyJWT==2.10.1
pylibacl==0.6.0
pyOpenSSL==21.0.0
pyparsing==2.4.7
pyproj==3.3.0
PyQt5==5.15.6
PyQt5-sip==12.9.1
pyrsistent==0.18.1
pytest==8.3.5
python-apt==2.4.0+ubuntu4
python-dateutil==2.8.2
python-debian==0.1.43+ubuntu1.1
python-dotenv==1.0.1
python-engineio==4.12.1
python-multipart==0.0.18
python-pptx==1.0.2
python-socketio==5.13.0
pythran==0.10.0
pytz==2022.1
pyxattr==0.7.2
pyxdg==0.27
PyYAML==5.4.1
pyzmq==25.1.0
QScintilla==2.11.6
queuelib==1.6.2
referencing==0.36.2
regex==2024.11.6
reportlab==3.6.8
requests==2.32.3
requests-toolbelt==1.0.0
rfc3986==1.5.0
rich==13.9.4
rich-toolkit==0.13.2
rpds-py==0.25.0
rsa==4.9
ruamel.yaml==0.18.5
ruamel.yaml.clib==0.2.8
scipy==1.8.0
scmrepo==1.5.0
Scrapy==2.5.1
seaborn==0.13.2
SecretStorage==3.3.1
semver==3.0.2
service-identity==18.1.0
shellingham==1.5.4
shortuuid==1.0.11
shtab==1.6.4
simple-websocket==1.1.0
six==1.16.0
smmap==5.0.1
sniffio==1.3.0
soupsieve==2.3.1
SpeechRecognition==3.14.1
SQLAlchemy==2.0.38
sqlmodel==0.0.23
sqltrie==0.9.0
sse-starlette==2.3.5
stack-data==0.6.2
starlette==0.41.3
streamlit==1.42.2
sympy==1.9
syncer==2.0.3
systemd-python==234
tabulate==0.9.0
tenacity==8.2.3
texttable==1.6.4
tiktoken==0.9.0
tokenizers==0.21.1
toml==0.10.2
tomli==2.2.1
tomlkit==0.12.3
tornado==6.3.2
tqdm==4.66.1
traceloop-sdk==0.40.5
traitlets==5.9.0
treelib==1.6.4
Twisted==22.1.0
typer==0.15.2
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2023.3
ubuntu-drivers-common==0.0.0
ubuntu-pro-client==8001
ufoLib2==0.13.1
ufw==0.36.1
unattended-upgrades==0.1
unicodedata2==14.0.0
uptrace==1.31.0
urllib3==1.26.5
usb-creator==0.3.7
uv==0.6.3
uvicorn==0.34.0
uvloop==0.21.0
validators==0.18.2
vine==5.1.0
virtualenv==20.24.0
virtualenv-clone==0.5.7
voluptuous==0.14.1
w3lib==1.22.0
wadllib==1.3.6
watchdog==6.0.0
watchfiles==0.20.0
wcwidth==0.2.6
webencodings==0.5.1
websocket-client==1.2.3
websockets==14.2
wrapt==1.17.2
wsproto==1.2.0
xdg==5
xkit==0.0.0
xlrd==2.0.1
XlsxWriter==3.2.2
xxhash==3.5.0
yarl==1.20.0
youtube-transcript-api==0.6.3
zc.lockfile==3.0.post1
zipp==3.21.0
zope.interface==5.4.0
zstandard==0.23.0
mlflow==2.10.2
mlflow-skinny==2.10.2
