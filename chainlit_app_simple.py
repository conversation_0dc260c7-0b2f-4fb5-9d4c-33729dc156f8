"""Chainlit + LangGraph Copilot with Plotly support
====================================================
Version corrigée - Résout les problèmes d'affichage des graphiques,
améliore le streaming et la gestion des erreurs.
"""

###############################################################################
# 0. Standard library imports & environment tweaks
###############################################################################

import os
import sys
import uuid
import json
import pickle
import asyncio
import pprint
from typing import Dict, List, Any, Optional

# Make sure Python streams are unbuffered so print() shows up immediately.
# (Works only when the process is launched by `python -u`, but we do our part.)
os.environ.setdefault("PYTHONUNBUFFERED", "1")

###############################################################################
# 1. Third‑party imports  
###############################################################################

from dotenv import load_dotenv

import chainlit as cl
from chainlit.types import ThreadDict
from chainlit.data.sql_alchemy import SQLAlchemyDataLayer

from langchain_core.messages import HumanMessage, ToolMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver

# Local modules (your original project structure)
from main_copilot import (
    create_agent,
    make_postgres_checkpointer,  # still used elsewhere
    AgentState,
    InputData,
    _merge,
    ensure_message_ids,
    remove_duplicate_messages,
    get_prompt_for_partner,
)
from sandbox_client import SandboxClient
from plot_template import apply_company_style  # Import the plot styling function

###############################################################################
# 2. Environment & data‑layer initialisation
###############################################################################

print("Attempting to load .env from CWD…")
load_dotenv(override=True)

DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "chainlit_db")
DB_USER = os.getenv("POSTGRES_USER", "user_test")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "password_test")
DB_URI_LANGGRAPH = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# ---------------------------------------------------------------------------
@cl.data_layer
def get_data_layer():
    conninfo = (
        "postgresql+asyncpg://" f"{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    )
    print("[DEBUG CL DATA_LAYER] Initialising SQLAlchemyDataLayer…")
    return SQLAlchemyDataLayer(conninfo=conninfo, storage_provider=None)

###############################################################################
# 3. OAuth callback (unchanged)
###############################################################################

@cl.oauth_callback
def oauth_callback(
    provider_id: str,
    token: str,
    raw_user_data: Dict[str, str],
    default_user: cl.User,
) -> Optional[cl.User]:
    print(f"OAuth callback for provider: {provider_id}")
    return default_user

###############################################################################
# 4. Helpers
###############################################################################

def _parse_tool_content(content: Any) -> Dict[str, Any]:
    if isinstance(content, str):
        try: return json.loads(content)
        except json.JSONDecodeError: return {"raw_content": content, "error": "Not valid JSON"}
    if isinstance(content, dict): return content
    return {"raw_content": str(content), "error": "Unknown content type"}


def serialise_state(state: AgentState) -> dict:  # type: ignore[valid‑type]
    """Make AgentState printable (messages → small dicts)."""

    def _msg_to_dict(m):  # noqa: ANN001
        if isinstance(m, (HumanMessage, AIMessage, ToolMessage)):
            return {
                "type": m.__class__.__name__,
                "id": getattr(m, "id", None),
                "content": m.content if isinstance(m.content, str) else "<complex>",
            }
        return str(m)

    # Create a copy of the state to avoid modifying the original
    serialized = dict(state)
    
    # Handle messages specially
    if "messages" in serialized:
        serialized["messages"] = [_msg_to_dict(x) for x in serialized["messages"]]
    
    # Ensure output_image_paths is preserved
    if "output_image_paths" in serialized:
        serialized["output_image_paths"] = list(serialized["output_image_paths"])
    
    return serialized

###############################################################################
# 5. LangGraph initialisation helper 
###############################################################################

async def initialize_langgraph_components(thread_id: str, partner_name: str):
    """Create/restore checkpointer + agent + state for the given thread."""

    print(f"[DEBUG LG] Initialising components for thread_id={thread_id}")

    # 5.1  Checkpointer context manager ------------------------------------------------
    checkpointer_cm = AsyncPostgresSaver.from_conn_string(DB_URI_LANGGRAPH)
    try:
        cp_instance = await checkpointer_cm.__aenter__()
        await cp_instance.setup()
    except Exception as exc:  # noqa: BLE001
        print(f"[ERROR LG] Checkpointer setup failed: {exc}")
        cp_instance = None

    cl.user_session.set("lg_checkpointer_cm", checkpointer_cm)
    cl.user_session.set("lg_checkpointer_instance", cp_instance)

    # 5.2  Agent ----------------------------------------------------------------------
    if cp_instance:
        lg_agent = create_agent(checkpointer=cp_instance, partner=partner_name) 
        cl.user_session.set("lg_agent", lg_agent)
    else:
        cl.user_session.set("lg_agent", None)

    # 5.3  Agent state ----------------------------------------------------------------
    if cp_instance:
        cfg = RunnableConfig(configurable={"thread_id": thread_id})
        try:
            persisted = await cp_instance.aget(cfg) or {}
        except Exception:  # noqa: BLE001
            persisted = {}
    else:
        persisted = {}

    if persisted:
        # rebuild messages list into proper objects
        rebuilt: list[Any] = []
        for md in persisted.get("messages", []):
            if isinstance(md, (HumanMessage, AIMessage, ToolMessage)):
                rebuilt.append(md)
            elif isinstance(md, dict):
                typ = md.get("type", "").lower()
                if "human" in typ:
                    rebuilt.append(HumanMessage(**md))
                elif "ai" in typ:
                    rebuilt.append(AIMessage(**md))
                elif "tool" in typ:
                    rebuilt.append(ToolMessage(**md))
        curr_state: AgentState = {
            **persisted,
            "messages": rebuilt,
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
        }  # type: ignore[assignment]
    else:
        curr_state = {
            "messages": [],
            "remaining_steps": 25,
            "input_data": [],
            "intermediate_outputs": [],
            "current_variables": {},
            "output_image_paths": [],
            "data_description": [],
            "generic_parser_request": [],
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
            "partner_config": {},
            "summary": "",
            "id_last_summary": None,
        }  # type: ignore[assignment]

    cl.user_session.set("lg_agent_state", curr_state)
    cl.user_session.set("thread_id", thread_id)
    cl.user_session.set(
        "langgraph_initialized_for_thread", bool(cp_instance)
    )

###############################################################################
# 6. Chat‑lifecycle callbacks
###############################################################################

@cl.on_chat_start
async def on_chat_start():
    pn = os.getenv("DEFAULT_PARTNER", "oksigen")
    print(f"[DEBUG CL] on_chat_start: partner_name={pn}")
    cl.user_session.set("partner_name", pn)
    cl.user_session.set("langgraph_initialized_for_thread", False)
    cl.user_session.set("displayed_plot_filenames", set())  # Initialize empty set for tracking displayed plots
    await cl.Message(content=f"Agent initialisé (Partenaire {pn}). Dites‑moi …").send()


@cl.on_chat_resume
async def on_chat_resume(thread: ThreadDict):
    tid = thread["id"]
    pn = thread.get("metadata", {}).get("partner_name", os.getenv("DEFAULT_PARTNER", "oksigen"))
    await initialize_langgraph_components(tid, pn)
    st = cl.user_session.get("lg_agent_state", {"messages": []})
    cl.user_session.set("displayed_plot_filenames", set())  # Reset displayed plots set on resume
    await cl.Message(
        content=f"Conversation reprise (Partenaire {pn}). {len(st.get('messages', []))} messages enregistrés."
    ).send()

###############################################################################
# 7. Main on_message handler  
###############################################################################

# Clés utilisées pour identifier les graphiques dans les payloads des outils
IMAGE_KEYS = ("output_image_paths", "plots", "IMAGES tool", "IMAGES")


@cl.on_message
async def on_message(msg_event: cl.Message):
    active_thread_id = cl.context.session.thread_id
    if not active_thread_id:
        # ... (error handling)
        print(f"[CRITICAL CL] cl.context.session.thread_id is None in on_message!")
        await cl.Message(content="Erreur critique: Impossible d'identifier la session.").send()
        cl.user_session.set("langgraph_initialized_for_thread", False)
        return

    # Get the set of already displayed plots for this session
    displayed_plots_session = cl.user_session.get("displayed_plot_filenames", set())

    if not cl.user_session.get("langgraph_initialized_for_thread") or \
       cl.user_session.get("thread_id") != active_thread_id:
        print(f"[DEBUG CL] Initializing/Re-syncing LangGraph for thread_id: {active_thread_id}")
        partner_name_for_init = cl.user_session.get("partner_name", os.getenv("DEFAULT_PARTNER", "oksigen"))
        await initialize_langgraph_components(active_thread_id, partner_name_for_init)

    lg_agent = cl.user_session.get("lg_agent")
    # lg_agent_state is the state that WE will manage and update in the session.
    # It starts as the state from the beginning of the turn.
    lg_agent_state: Optional[AgentState] = cl.user_session.get("lg_agent_state")
    partner_name = cl.user_session.get("partner_name")

    print(f"\n[DEBUG CL] === Turn Start for Thread: {active_thread_id}, User Msg ID: {msg_event.id} ===")

    sandbox_client: Optional[SandboxClient] = cl.user_session.get("sandbox_client")
    if not sandbox_client:
        try:
            sandbox_client = SandboxClient()
            cl.user_session.set("sandbox_client", sandbox_client)
            print("[DEBUG CL] SandboxClient instantiated.")
        except Exception as e:
            print(f"[ERROR CL] Failed to instantiate SandboxClient: {e}")
            await cl.Message(content="Erreur de configuration du client Sandbox.").send()
            return

    if not lg_agent or not lg_agent_state or not partner_name:
        await cl.Message(content="Erreur: Agent non initialisé. Veuillez rafraîchir.").send()
        print(f"[ERROR CL] Crucial LangGraph components missing for thread {active_thread_id}.")
        return

    human_message_obj = HumanMessage(content=msg_event.content, id=str(uuid.uuid4()))
    # Messages to send to the agent: current state messages + new human message
    current_messages_for_input = list(lg_agent_state.get("messages", [])) # Make a copy
    messages_for_lg_agent_input = current_messages_for_input + [human_message_obj]

    config_for_run = RunnableConfig(
        configurable={"thread_id": active_thread_id, "session_id": active_thread_id, "partner": partner_name}
    )

    async with cl.Step(
        name="Agent Processing", type="run", parent_id=msg_event.id, id=f"agent_proc_{uuid.uuid4().hex[:4]}"
    ) as agent_processing_step:
        agent_processing_step.input = msg_event.content
        await agent_processing_step.send()
        print(f"[DEBUG CL] 'Agent Processing' step (ID: {agent_processing_step.id}) sent.")

        assistant_final_msg = cl.Message(content="", author="Assistant", parent_id=agent_processing_step.id)
        streamed_text_content = ""
        
        # These are for the current turn's processing
        new_plot_elements_this_turn: List[cl.Plotly] = []  # Only store new plots for this turn
        processed_plot_filenames_this_turn = set()
        processed_tool_ids_this_turn = set()
        
        # This will store the *final* state dictionary from the graph execution.
        # In "updates" mode, the last chunk for a successful run is the final state.
        final_graph_state_dict: Optional[dict] = None 

        try:
            current_run_lg_agent_state_dict = None
            messages_from_current_run = []  # Track messages from this run
            
            async for chunk in lg_agent.astream({"messages": messages_for_lg_agent_input}, config=config_for_run, stream_mode="updates"):
                print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
                print(f"[CURRENT STATE]: {chunk}")
                print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
                
                # Store the current state from the chunk
                if isinstance(chunk, dict):
                    # Handle tools chunk
                    if "tools" in chunk and isinstance(chunk["tools"], dict):
                        # ── inside the chunk["tools"] handler ────────────────────────────
                        if "output_image_paths" in chunk["tools"]:
                            current_run_lg_agent_state_dict = chunk["tools"]
                            print(f"[DEBUG PLOT STATE] Found plots in tools chunk: {chunk['tools']['output_image_paths']}")

                            for plot_path in chunk["tools"]["output_image_paths"]:
                                # NEW: plot only files that start with the expected prefix
                                if not plot_path.startswith("plotly_fig_"):
                                    print(f"[DEBUG PLOT SKIP] '{plot_path}' ignored (doesn't match prefix).")
                                    continue

                                # Skip if we've already shown it
                                if plot_path in displayed_plots_session:
                                    print(f"[DEBUG PLOT SKIP] Skipping already displayed plot: {plot_path}")
                                    continue
                                if plot_path in processed_plot_filenames_this_turn:
                                    print(f"[DEBUG PLOT SKIP] Skipping plot already processed this turn: {plot_path}")
                                    continue

                                try:
                                    pickle_bytes = await sandbox_client.download_plot(
                                        session_id=active_thread_id,
                                        plot_name=plot_path,
                                    )
                                    if pickle_bytes:
                                        fig_obj = pickle.loads(pickle_bytes)
                                        fig_obj = apply_company_style(fig_obj)   # apply company  
                                        plot_elem = cl.Plotly(
                                            name=os.path.basename(plot_path).rsplit(".", 1)[0],
                                            figure=fig_obj,
                                            display="inline",
                                            size="large",
                                        )
                                        new_plot_elements_this_turn.append(plot_elem)
                                        displayed_plots_session.add(plot_path)
                                        processed_plot_filenames_this_turn.add(plot_path)
                                        print(f"[DEBUG PLOT] Added new plot: {plot_path}")
                                except Exception as e:
                                    print(f"[ERROR PLOT] Error processing plot {plot_path}: {e}")
                                    import traceback; traceback.print_exc()
                    
                    # Handle agent chunk
                    elif "agent" in chunk and isinstance(chunk["agent"], dict):
                        if "messages" in chunk["agent"]:
                            current_run_lg_agent_state_dict = chunk["agent"]
                            print(f"[DEBUG PLOT STATE] Found agent chunk with messages")
                            
                            # Process messages for streaming and state
                            for msg in chunk["agent"]["messages"]:
                                messages_from_current_run.append(msg)  # Add to our message list
                                if isinstance(msg, AIMessage):
                                    content = msg.content if isinstance(msg.content, str) else ""
                                    if content and content != streamed_text_content:
                                        if not assistant_final_msg.id:
                                            await assistant_final_msg.send()
                                        await assistant_final_msg.stream_token(content)
                                        streamed_text_content = content
                                        print(f"[DEBUG MESSAGE] Streamed content: {content[:100]}...")

            # Update the final message with any new plots
            if new_plot_elements_this_turn:
                print(f"[DEBUG PLOT] Attaching {len(new_plot_elements_this_turn)} new plots to final message")
                if not assistant_final_msg.id:
                    await assistant_final_msg.send()
                assistant_final_msg.elements = new_plot_elements_this_turn
                await assistant_final_msg.update()

            # Update the session's displayed plots set
            cl.user_session.set("displayed_plot_filenames", displayed_plots_session)

            # ── persist new state & debug‑print ---------------------------------------------
            if lg_agent_state is not None:
                # add messages from this turn, dedupe
                lg_state_msgs = lg_agent_state.get("messages", []) + ensure_message_ids(messages_from_current_run)  # type: ignore[arg-type]
                lg_agent_state["messages"], _ = remove_duplicate_messages(lg_state_msgs)
                
                # Preserve output_image_paths from the current run
                if current_run_lg_agent_state_dict:
                    if "output_image_paths" in current_run_lg_agent_state_dict:
                        lg_agent_state["output_image_paths"] = current_run_lg_agent_state_dict["output_image_paths"]
                        print(f"[DEBUG PLOT STATE] Updated state with plots: {lg_agent_state['output_image_paths']}")
                
                cl.user_session.set("lg_agent_state", lg_agent_state)
                print("[DEBUG LG STATE]", pprint.pformat(serialise_state(lg_agent_state), width=100, compact=True))

        except Exception as e_stream: # Catches errors from the agent.astream() loop or the plot state check
            print(f"[ERROR CL STREAM] Exception during agent stream or plot state check: {e_stream}")
            import traceback; traceback.print_exc(file=sys.stdout)
            # Error message will be set on UI elements in the finally block

        # This 'finally' ensures UI elements are updated even if an error occurred inside the 'try'
        finally:
            if not assistant_final_msg.id and not streamed_text_content:
                assistant_final_msg.content = "Traitement terminé."
            try:
                if not assistant_final_msg.id: await assistant_final_msg.send()
                else: await assistant_final_msg.update()
            except Exception as e_ui: print(f"[ERROR CL UI] Final assistant msg update failed: {e_ui}")

            if new_plot_elements_this_turn:
                print(f"[DEBUG PLOT FINAL ATTACH] Attaching {len(new_plot_elements_this_turn)} new plots to final message.")
                if not assistant_final_msg.id: # Should exist by now
                    try: await assistant_final_msg.send()
                    except Exception as e_ui_send: print(f"[ERROR CL UI] Final assistant msg send before elements failed: {e_ui_send}")
                
                if assistant_final_msg.id: # Re-check in case send failed
                    assistant_final_msg.elements = new_plot_elements_this_turn
                    try: await assistant_final_msg.update()
                    except Exception as e_ui_elem: print(f"[ERROR CL UI] Final assistant msg update with elements failed: {e_ui_elem}")
                else:
                    print(f"[WARN PLOT FINAL ATTACH] Could not attach plots, final message has no ID.")
            
            agent_processing_step.output = streamed_text_content or assistant_final_msg.content or "Traitement terminé."
            try: await agent_processing_step.update()
            except Exception as e_ui_step: print(f"[ERROR CL UI] Agent proc step final update failed: {e_ui_step}")


    # --- Update cl.user_session's lg_agent_state with the state *after* the graph run ---
    # The final_graph_state_dict holds the complete state from the end of the graph execution.
    # The lg_agent_state in cl.user_session needs to be replaced by this.
    if final_graph_state_dict and isinstance(final_graph_state_dict, dict):
        # Reconstruct messages if they are dicts
        raw_messages = final_graph_state_dict.get("messages", [])
        rebuilt_messages = []
        for m_data in raw_messages:
            if isinstance(m_data, (HumanMessage, AIMessage, ToolMessage)): rebuilt_messages.append(m_data)
            elif isinstance(m_data, dict):
                m_type = m_data.get("type","").lower()
                m_content = m_data.get("content")
                m_id = m_data.get("id", str(uuid.uuid4()))
                if "human" in m_type: rebuilt_messages.append(HumanMessage(content=m_content, id=m_id))
                elif "ai" in m_type: rebuilt_messages.append(AIMessage(content=m_content, id=m_id, tool_calls=m_data.get("tool_calls",[])))
                elif "tool" in m_type: rebuilt_messages.append(ToolMessage(content=m_content, id=m_id, name=m_data.get("name"), tool_call_id=m_data.get("tool_call_id")))
        
        # Ensure the input human message is present if the graph somehow dropped it (shouldn't happen with add_messages)
        # This is more of a safety check; ideally, the graph's output is the source of truth.
        # if not any(getattr(m, 'id', None) == human_message_obj.id for m in rebuilt_messages):
        #    rebuilt_messages.append(human_message_obj) # Potentially adds it if missing

        final_messages_for_state = remove_duplicate_messages(ensure_message_ids(rebuilt_messages))
        if isinstance(final_messages_for_state, tuple): final_messages_for_state = final_messages_for_state[0]

        # Construct the new state for the session
        # Important: use active_thread_id and partner_name from the current Chainlit context
        new_session_state = {
            **final_graph_state_dict, # Spread all keys from the graph's output
            "messages": final_messages_for_state,
            "conversation_id": active_thread_id, # Override with current
            "session_id": active_thread_id,      # Override with current
            "partner": partner_name,             # Override with current
        }
        # Re-hydrate InputData if necessary
        if "input_data" in new_session_state and new_session_state["input_data"]:
            new_session_state["input_data"] = [
                InputData(**data) if isinstance(data, dict) and not isinstance(data, InputData) else data 
                for data in new_session_state["input_data"]
            ]
        
        cl.user_session.set("lg_agent_state", new_session_state) # type: ignore
        print(f"[DEBUG CL SESSION_STATE] Updated user session lg_agent_state from final graph state. Msgs: {len(new_session_state.get('messages',[]))}")
    
    elif lg_agent_state: # Fallback: if graph somehow didn't yield a final_graph_state_dict
        # Add human message to the *old* state if it's not there (less ideal)
        if not any(getattr(m, 'id', None) == human_message_obj.id for m in lg_agent_state.get("messages", [])):
            lg_agent_state.setdefault("messages", []).append(human_message_obj)
            cl.user_session.set("lg_agent_state", lg_agent_state)
            print(f"[WARN CL SESSION_STATE] No final_graph_state_dict from run. Updated old session state with human msg.")
    else:
        print(f"[ERROR CL SESSION_STATE] lg_agent_state is None and no final_graph_state_dict. Cannot update session state.")

    print(f"[DEBUG CL] === Turn End for Thread: {active_thread_id} ===\n")