#!/usr/bin/env python3
"""
Test script for Azure OpenAI model initialization
"""

import os
from dotenv import load_dotenv
from langchain.chat_models import init_chat_model

# Load environment variables from .env file
load_dotenv()

def test_azure_openai_model():
    """Test Azure OpenAI model initialization"""

    print("🔧 Setting up Azure OpenAI environment variables...")

    # Set up environment variables using dotenv (loaded from .env file)
    os.environ["AZURE_OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY", "")
    os.environ["AZURE_OPENAI_ENDPOINT"] = os.getenv("AZURE_OPENAI_ENDPOINT", "")
    os.environ["OPENAI_API_VERSION"] = "2025-03-01-preview"
    os.environ["AZURE_OPENAI_DEPLOYMENT_NAME"] = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME", "gpt-4.1")

    # Check if required environment variables are set
    if not os.environ["AZURE_OPENAI_API_KEY"]:
        print("❌ Error: OPENAI_API_KEY environment variable is not set")
        return False

    if not os.environ["AZURE_OPENAI_ENDPOINT"]:
        print("❌ Error: AZURE_OPENAI_ENDPOINT environment variable is not set")
        return False

   
    print(f"📋 Endpoint: {os.environ['AZURE_OPENAI_ENDPOINT']}")
    print(f"📋 API Version: {os.environ['OPENAI_API_VERSION']}")
    print(f"📋 Deployment: {os.environ['AZURE_OPENAI_DEPLOYMENT_NAME']}")

    try:
        print("\n🚀 Initializing Azure OpenAI model...")

        # Using init_chat_model with your exact format
        llm = init_chat_model(
            "azure_openai:gpt-4.1",
            azure_deployment=os.environ["AZURE_OPENAI_DEPLOYMENT_NAME"],
        )
        print(f"✅ Successfully initialized Azure OpenAI model using init_chat_model")

        # Test a simple chat completion
        print("\n🧪 Testing model with a simple message...")
        response = llm.invoke("Hello! Can you respond with 'Model is working correctly'?")
        print(f"📤 Response: {response.content}")

        return True

    except Exception as e:
        print(f"❌ Error initializing Azure OpenAI model: {str(e)}")
        print(f"🔍 Error type: {type(e).__name__}")

        # Provide specific troubleshooting based on error type
        if "authentication" in str(e).lower() or "unauthorized" in str(e).lower():
            print("💡 Troubleshooting: Check your API key and endpoint")
        elif "deployment" in str(e).lower():
            print("💡 Troubleshooting: Check your Azure deployment name")
        elif "version" in str(e).lower():
            print("💡 Troubleshooting: Check your API version")

        return False

def main():
    """Main function to run the test"""
    print("🔬 Azure OpenAI Model Test Script")
    print("=" * 40)
    
    success = test_azure_openai_model()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 Test completed successfully!")
    else:
        print("💥 Test failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    main()
