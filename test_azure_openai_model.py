#!/usr/bin/env python3
"""
Test script for Azure OpenAI model initialization
"""

import os
from langchain_openai import AzureChatOpenAI

def test_azure_openai_model():
    """Test Azure OpenAI model initialization"""

    print("🔧 Setting up Azure OpenAI environment variables...")

    # Set up environment variables
    # Note: Fixed the AZURE_OPENAI_ENDPOINT to use the correct endpoint value
    api_key = os.getenv("OPENAI_API_KEY", "")
    endpoint = os.getenv("AZURE_OPENAI_ENDPOINT", "")
    api_version = "2024-02-15-preview"  # Fixed: using a valid API version

    # Check if required environment variables are set
    if not api_key:
        print("❌ Error: OPENAI_API_KEY environment variable is not set")
        return False

    if not endpoint:
        print("❌ Error: AZURE_OPENAI_ENDPOINT environment variable is not set")
        return False

    print(f"📋 API Key: {'*' * 10}...{api_key[-4:] if len(api_key) > 4 else 'SET'}")
    print(f"📋 Endpoint: {endpoint}")
    print(f"📋 API Version: {api_version}")

    try:
        print("\n🚀 Initializing Azure OpenAI model...")

        # Using init_chat_model as requested
        model = init_chat_model(
            "gpt-4",
            model_provider="azure_openai",
            azure_endpoint=endpoint,
            api_key=api_key,
            api_version=api_version,
            azure_deployment="gpt-4",  # You may need to adjust this to your deployment name
            temperature=0,
            max_retries=0
        )
        print(f"✅ Successfully initialized Azure OpenAI model using init_chat_model")

        # Test a simple chat completion
        print("\n🧪 Testing model with a simple message...")
        response = model.invoke("Hello! Can you respond with 'Model is working correctly'?")
        print(f"📤 Response: {response.content}")

        return True

    except Exception as e:
        print(f"❌ Error initializing Azure OpenAI model: {str(e)}")
        print(f"🔍 Error type: {type(e).__name__}")

        # Provide specific troubleshooting based on error type
        if "authentication" in str(e).lower() or "unauthorized" in str(e).lower():
            print("💡 Troubleshooting: Check your API key and endpoint")
        elif "deployment" in str(e).lower():
            print("💡 Troubleshooting: Check your Azure deployment name")
        elif "version" in str(e).lower():
            print("💡 Troubleshooting: Check your API version")

        return False

def main():
    """Main function to run the test"""
    print("🔬 Azure OpenAI Model Test Script")
    print("=" * 40)
    
    success = test_azure_openai_model()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 Test completed successfully!")
    else:
        print("💥 Test failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    main()
