#!/usr/bin/env python3
"""
Robust script to view all plots for a given session ID.
This version handles plot deserialization issues and provides better error recovery.

Usage:
    python view_session_plots_robust.py <session_id>
    
Example:
    python view_session_plots_robust.py ed32d644-f65e-42aa-88d6-4576837874fc
"""

import sys
import os
import pickle
import asyncio
import argparse
from typing import List, Dict, Any, Optional
import webbrowser
import tempfile
import base64
import numpy as np

# Add the current directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from sandbox_client import SandboxClient
    from plot_template import apply_company_style
    import plotly.graph_objects as go
    import plotly.offline as pyo
    from plotly.subplots import make_subplots
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure you have plotly installed: pip install plotly")
    sys.exit(1)


class RobustSessionPlotViewer:
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.sandbox_client = SandboxClient()
        self.plots_data: List[Dict[str, Any]] = []
    
    def fix_plotly_data(self, obj):
        """Recursively fix plotly data structures that have serialization issues."""
        if isinstance(obj, dict):
            # Check for the problematic bdata format
            if 'dtype' in obj and 'bdata' in obj:
                try:
                    # Decode base64 data and convert to numpy array
                    data_bytes = base64.b64decode(obj['bdata'])
                    dtype = np.dtype(obj['dtype'])
                    array = np.frombuffer(data_bytes, dtype=dtype)
                    return array.tolist()  # Convert to list for plotly
                except Exception as e:
                    print(f"[DEBUG] Failed to decode bdata: {e}")
                    return None
            else:
                # Recursively fix nested dictionaries
                return {k: self.fix_plotly_data(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            # Recursively fix lists
            return [self.fix_plotly_data(item) for item in obj]
        else:
            return obj
    
    async def get_plots_from_execution(self) -> List[str]:
        """Get plots by executing a simple command that triggers plot discovery."""
        try:
            print(f"[DEBUG] Getting plots by executing code in session: {self.session_id}")
            
            # Execute a simple command that will trigger the sandbox to report plots
            code = """
# Simple command to trigger plot discovery
print("Checking for plots...")
"""
            
            result = await self.sandbox_client.execute_code(
                session_id=self.session_id,
                code=code,
                timeout_seconds=10
            )
            
            print(f"[DEBUG] Execution result keys: {list(result.keys())}")
            
            # Get plots from the execution result
            plots = result.get("plots", [])
            print(f"[DEBUG] Found plots in execution result: {plots}")
            
            return plots
            
        except Exception as e:
            print(f"[DEBUG] Error getting plots from execution: {str(e)}")
            return []
    
    async def get_plot_list(self) -> List[str]:
        """Get list of plot files for the session."""
        # Try to get plots from execution result first
        plots = await self.get_plots_from_execution()
        
        if not plots:
            print("[DEBUG] No plots found in execution result, trying session info...")
            # Fallback to session info
            try:
                session_info = await self.sandbox_client.get_session_info(self.session_id)
                plots = session_info.get("plots", [])
            except Exception as e:
                print(f"[DEBUG] Error getting session info: {str(e)}")
                plots = []
        
        print(f"[DEBUG] Raw plots found: {plots}")
        
        # Filter out plots that start with 'plotly_plotly' as requested
        filtered_plots = [
            plot for plot in plots 
            if not plot.startswith("plotly_plotly")
            and plot.endswith(".pickle")
            and ("plotly" in plot.lower() or "fig" in plot.lower())
        ]
        
        print(f"Found {len(plots)} total plots, {len(filtered_plots)} after filtering")
        print(f"Filtered plots: {filtered_plots}")
        
        return filtered_plots
    
    async def download_plot(self, plot_name: str) -> Optional[go.Figure]:
        """Download and deserialize a plot with error recovery."""
        try:
            print(f"Downloading plot: {plot_name}")
            plot_bytes = await self.sandbox_client.download_plot(
                session_id=self.session_id,
                plot_name=plot_name
            )
            
            if not plot_bytes:
                raise Exception(f"No data received for plot {plot_name}")
            
            # Deserialize the pickle
            raw_fig = pickle.loads(plot_bytes)
            
            if not isinstance(raw_fig, go.Figure):
                raise Exception(f"Plot {plot_name} is not a Plotly Figure object, got {type(raw_fig)}")
            
            print(f"[DEBUG] Successfully loaded figure: {plot_name}")
            
            # Try to fix any data serialization issues
            try:
                # Convert figure to dict, fix it, then recreate
                fig_dict = raw_fig.to_dict()
                fixed_dict = self.fix_plotly_data(fig_dict)
                fig = go.Figure(fixed_dict)
                print(f"[DEBUG] Successfully fixed figure data: {plot_name}")
            except Exception as fix_error:
                print(f"[DEBUG] Could not fix figure data for {plot_name}: {fix_error}")
                # Try to use the original figure
                fig = raw_fig
            
            # Apply company styling
            try:
                fig = apply_company_style(fig)
                print(f"[DEBUG] Applied company styling: {plot_name}")
            except Exception as style_error:
                print(f"[DEBUG] Could not apply styling to {plot_name}: {style_error}")
            
            return fig
            
        except Exception as e:
            print(f"❌ Error downloading plot {plot_name}: {str(e)}")
            print(f"[DEBUG] Full error details:")
            import traceback
            traceback.print_exc()
            return None
    
    async def recreate_plot_from_variables(self) -> Optional[go.Figure]:
        """Try to recreate plots from session variables if direct download fails."""
        try:
            print("[DEBUG] Attempting to recreate plot from session variables...")
            
            # Execute code to recreate the plot from variables
            code = """
import plotly.graph_objects as go
import plotly.express as px

# Try to recreate plots from existing variables
recreated_figures = []

# Check if we have the main figure variable
if 'fig' in globals() and hasattr(fig, 'data'):
    print("Found 'fig' variable, attempting to recreate...")
    try:
        # Create a new figure with the same data
        new_fig = go.Figure()
        for trace in fig.data:
            new_fig.add_trace(trace)
        new_fig.update_layout(fig.layout)
        recreated_figures.append(new_fig)
        print("Successfully recreated figure from 'fig' variable")
    except Exception as e:
        print(f"Failed to recreate from 'fig': {e}")

# Check if we have plotly_figures list
if 'plotly_figures' in globals() and isinstance(plotly_figures, list):
    print(f"Found 'plotly_figures' list with {len(plotly_figures)} items")
    for i, fig_item in enumerate(plotly_figures):
        try:
            if hasattr(fig_item, 'data'):
                new_fig = go.Figure()
                for trace in fig_item.data:
                    new_fig.add_trace(trace)
                new_fig.update_layout(fig_item.layout)
                recreated_figures.append(new_fig)
                print(f"Successfully recreated figure {i} from plotly_figures")
        except Exception as e:
            print(f"Failed to recreate figure {i}: {e}")

# If we have data, try to create a simple plot
if 'top10' in globals() and hasattr(top10, 'columns'):
    print("Found 'top10' DataFrame, creating simple bar chart...")
    try:
        # Create a simple bar chart from the top10 data
        fig = px.bar(top10, x=top10.columns[0], y=top10.columns[1], 
                     title="Top 10 Sites by Consumption")
        recreated_figures.append(fig)
        print("Successfully created bar chart from top10 data")
    except Exception as e:
        print(f"Failed to create chart from top10: {e}")

print(f"Total recreated figures: {len(recreated_figures)}")

# Save the first recreated figure if any
if recreated_figures:
    recreated_fig = recreated_figures[0]
    print("Recreated figure successfully!")
else:
    print("No figures could be recreated")
"""
            
            result = await self.sandbox_client.execute_code(
                session_id=self.session_id,
                code=code,
                timeout_seconds=15
            )
            
            print(f"[DEBUG] Recreation result: {result.get('output', 'No output')}")
            
            # Check if new plots were created
            new_plots = result.get("plots", [])
            if new_plots:
                print(f"[DEBUG] New plots created during recreation: {new_plots}")
                # Try to download the newest plot
                for plot_name in reversed(new_plots):
                    if not plot_name.startswith("plotly_plotly"):
                        return await self.download_plot(plot_name)
            
            return None
            
        except Exception as e:
            print(f"[DEBUG] Error recreating plot from variables: {str(e)}")
            return None
    
    async def fetch_all_plots(self) -> List[Dict[str, Any]]:
        """Fetch all plots for the session."""
        plot_names = await self.get_plot_list()
        
        plots_data = []
        
        # Try to download existing plots
        for plot_name in plot_names:
            fig = await self.download_plot(plot_name)
            if fig:
                plots_data.append({
                    "name": plot_name,
                    "figure": fig,
                    "title": plot_name.replace(".pickle", "").replace("_", " ").title()
                })
        
        # If no plots were successfully downloaded, try to recreate from variables
        if not plots_data:
            print("No plots downloaded successfully, trying to recreate from variables...")
            recreated_fig = await self.recreate_plot_from_variables()
            if recreated_fig:
                plots_data.append({
                    "name": "recreated_plot",
                    "figure": recreated_fig,
                    "title": "Recreated Plot from Session Variables"
                })
        
        return plots_data
    
    def create_combined_view(self, plots_data: List[Dict[str, Any]]) -> Optional[go.Figure]:
        """Create a combined view with all plots."""
        if not plots_data:
            return None
        
        if len(plots_data) == 1:
            # Single plot - return as is with updated title
            fig = plots_data[0]["figure"]
            fig.update_layout(
                title=f"Session {self.session_id[:8]}... - {plots_data[0]['title']}",
                title_x=0.5
            )
            return fig
        
        # Multiple plots - create subplots
        num_plots = len(plots_data)
        
        # Calculate grid dimensions
        if num_plots <= 2:
            rows, cols = 1, num_plots
        elif num_plots <= 4:
            rows, cols = 2, 2
        else:
            rows = (num_plots + 2) // 3
            cols = 3
        
        # Create subplot titles
        subplot_titles = [plot["title"] for plot in plots_data]
        
        # Create subplots
        fig = make_subplots(
            rows=rows, 
            cols=cols,
            subplot_titles=subplot_titles,
            horizontal_spacing=0.1,
            vertical_spacing=0.15
        )
        
        # Add each plot to the subplots
        for i, plot_data in enumerate(plots_data):
            row = (i // cols) + 1
            col = (i % cols) + 1
            
            source_fig = plot_data["figure"]
            
            # Add all traces from the source figure
            for trace in source_fig.data:
                fig.add_trace(trace, row=row, col=col)
        
        # Update layout
        fig.update_layout(
            title=f"Session {self.session_id[:8]}... - All Plots ({num_plots} plots)",
            title_x=0.5,
            showlegend=False,
            height=300 * rows
        )
        
        # Apply company styling
        try:
            fig = apply_company_style(fig)
        except Exception as e:
            print(f"[DEBUG] Could not apply styling to combined figure: {e}")
        
        return fig
    
    def save_and_open_html(self, fig: go.Figure) -> Optional[str]:
        """Save the figure as HTML and open in browser."""
        if not fig:
            print("No figure to display.")
            return None
        
        try:
            # Create a temporary HTML file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
                html_file = f.name
            
            # Generate HTML
            html_content = pyo.plot(
                fig, 
                output_type='div', 
                include_plotlyjs=True,
                config={'displayModeBar': True, 'responsive': True}
            )
            
            # Wrap in a complete HTML document
            full_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Session Plots - {self.session_id[:8]}...</title>
                <meta charset="utf-8">
                <style>
                    body {{
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        background-color: #f5f5f5;
                    }}
                    .header {{
                        text-align: center;
                        margin-bottom: 20px;
                        padding: 20px;
                        background-color: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }}
                    .plot-container {{
                        background-color: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        padding: 20px;
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Session Plots Viewer</h1>
                    <p><strong>Session ID:</strong> {self.session_id}</p>
                    <p><strong>Number of Plots:</strong> {len(self.plots_data)}</p>
                    <p><strong>Note:</strong> Plots starting with 'plotly_plotly' are excluded</p>
                </div>
                <div class="plot-container">
                    {html_content}
                </div>
            </body>
            </html>
            """
            
            # Write to file
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(full_html)
            
            print(f"HTML file saved to: {html_file}")
            
            # Open in browser
            webbrowser.open(f'file://{html_file}')
            print("Opening in browser...")
            
            return html_file
            
        except Exception as e:
            print(f"Error saving HTML file: {str(e)}")
            return None
    
    async def view_plots(self):
        """Main method to fetch and display all plots."""
        try:
            print(f"🔍 Fetching plots for session: {self.session_id}")
            
            # Fetch all plots
            self.plots_data = await self.fetch_all_plots()
            
            if not self.plots_data:
                print("❌ No plots found to display.")
                print("This could mean:")
                print("  - No plots have been generated yet")
                print("  - All plots start with 'plotly_plotly' (which are excluded)")
                print("  - Session ID is incorrect")
                print("  - Plot files are corrupted")
                return
            
            print(f"✅ Successfully fetched {len(self.plots_data)} plots")
            
            # Create combined view
            combined_fig = self.create_combined_view(self.plots_data)
            
            # Save and open in browser
            html_file = self.save_and_open_html(combined_fig)
            
            if html_file:
                print(f"\n🎉 Plots displayed successfully!")
                print(f"📁 HTML file: {html_file}")
                print(f"🌐 The file should open automatically in your browser")
                print(f"💡 You can keep this file open or delete it when done.")
            else:
                print("❌ Failed to create HTML file")
            
        except Exception as e:
            print(f"❌ Error viewing plots: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            # Clean up the sandbox client
            if hasattr(self.sandbox_client, '_session') and self.sandbox_client._session:
                await self.sandbox_client._session.close()


async def main():
    parser = argparse.ArgumentParser(
        description="View all plots for a given session ID (robust version)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python view_session_plots_robust.py ed32d644-f65e-42aa-88d6-4576837874fc
  python view_session_plots_robust.py abc123def456 --list-only
        """
    )
    
    parser.add_argument(
        "session_id",
        help="The session ID to fetch plots for"
    )
    
    parser.add_argument(
        "--list-only",
        action="store_true",
        help="Only list available plots without displaying them"
    )
    
    args = parser.parse_args()
    
    if not args.session_id:
        print("Error: Session ID is required")
        parser.print_help()
        sys.exit(1)
    
    viewer = RobustSessionPlotViewer(args.session_id)
    
    if args.list_only:
        try:
            plots = await viewer.get_plot_list()
            print(f"\nAvailable plots for session {args.session_id}:")
            for i, plot in enumerate(plots, 1):
                print(f"  {i}. {plot}")
        except Exception as e:
            print(f"Error listing plots: {str(e)}")
        finally:
            if hasattr(viewer.sandbox_client, '_session') and viewer.sandbox_client._session:
                await viewer.sandbox_client._session.close()
    else:
        await viewer.view_plots()


if __name__ == "__main__":
    asyncio.run(main())
